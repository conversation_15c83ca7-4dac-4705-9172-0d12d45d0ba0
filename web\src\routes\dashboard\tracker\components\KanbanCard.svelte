<script lang="ts">
  import { Building, Calendar, MapPin, MoreHorizontal } from 'lucide-svelte';
  import { Button } from '$lib/components/ui/button';
  import { Card } from '$lib/components/ui/card';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Badge } from '$lib/components/ui/badge';
  import { statusColors, statusIcons } from '../types';

  let {
    application,
    openApplicationDetails,
    isSelected = false,
    onSelectionChange = () => {},
  }: {
    application: any;
    openApplicationDetails: (application: any) => void;
    isSelected: boolean;
    onSelectionChange: (selected: boolean) => void;
  } = $props();

  const cardClass = $derived(
    `cursor-pointer p-3 hover:shadow-md transition-all ${isSelected ? 'ring-2 ring-primary' : ''}`
  );

  function formatDate(dateStr: string) {
    if (!dateStr) return 'N/A';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return 'Invalid date';
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
    }).format(date);
  }
</script>

<Card
  class={cardClass}
  draggable="true"
  ondragstart={(e) => {
    e.dataTransfer?.setData('text/plain', application.id.toString());
  }}
  onclick={() => openApplicationDetails(application)}>
  <!-- Header with checkbox and more button -->
  <div class="mb-3 flex items-start justify-between">
    <Checkbox
      checked={isSelected}
      onCheckedChange={(checked) => onSelectionChange(!!checked)}
      onclick={(e) => e.stopPropagation()}
      aria-label="Select application"
      class="mt-0.5" />
  </div>

  <!-- Company logo and info -->
  <div class="mb-3 flex items-center gap-3">
    <div class="bg-muted h-10 w-10 flex-shrink-0 overflow-hidden rounded-lg">
      <img src={application.logo} alt={application.company} class="h-full w-full object-cover" />
    </div>
    <div class="min-w-0 flex-1">
      <h3 class="truncate text-sm font-semibold">{application.position}</h3>
      <div class="text-muted-foreground flex items-center text-xs">
        <Building class="mr-1 h-3 w-3 flex-shrink-0" />
        <span class="truncate">{application.company}</span>
      </div>
    </div>
  </div>

  <!-- Location -->
  <div class="text-muted-foreground mb-3 flex items-center text-xs">
    <MapPin class="mr-1 h-3 w-3 flex-shrink-0" />
    <span class="truncate">{application.location}</span>
  </div>

  <!-- Footer with date and status -->
  <div class="flex items-center justify-between">
    <div class="text-muted-foreground flex items-center text-xs">
      <Calendar class="mr-1 h-3 w-3" />
      <span>{formatDate(application.appliedDate)}</span>
    </div>

    <!-- Status badge (optional, since it's already in the column) -->
    {#if application.priority || application.urgent}
      <Badge variant="outline" class="text-xs">
        {application.priority || 'Urgent'}
      </Badge>
    {/if}
  </div>

  <!-- Job posting link -->
  {#if application.url}
    <div class="mt-2 border-t pt-2">
      <a
        href={application.url}
        target="_blank"
        rel="noopener noreferrer"
        class="text-primary text-xs hover:underline"
        onclick={(e) => e.stopPropagation()}>
        View Job Posting
      </a>
    </div>
  {/if}
</Card>
