<script>
  import FeatureCard from '../ui/FeatureCard.svelte';
  import { Zap, Target, Clock, BarChart, Shield, Globe, MessageSquare, Award } from 'lucide-svelte';

  const features = [
    {
      icon: Zap,
      title: 'One-Click Apply',
      description: 'Apply to hundreds of jobs across multiple platforms with a single click.',
    },
    {
      icon: Target,
      title: 'Smart Matching',
      description: 'Our AI matches your resume to job requirements for higher success rates.',
    },
    {
      icon: Clock,
      title: 'Save Hours Daily',
      description: 'Automate repetitive application tasks and focus on preparing for interviews.',
    },
    {
      icon: BarChart,
      title: 'Application Analytics',
      description: 'Track your application performance and optimize your job search strategy.',
    },
    {
      icon: Shield,
      title: 'Resume Optimization',
      description: 'Get suggestions to improve your resume for specific job postings.',
    },
    {
      icon: Globe,
      title: 'Multi-Platform Support',
      description: 'Works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more.',
    },
    {
      icon: MessageSquare,
      title: 'AI Interview Coach',
      description:
        'Practice with realistic mock interviews tailored to your industry with instant feedback.',
    },
    {
      icon: Award,
      title: 'Career Advancement',
      description: 'Get personalized recommendations for skills to develop for your dream roles.',
    },
  ];
</script>

<section id="features" class="p-12">
  <div class="grid grid-cols-1 divide-x divide-y border md:grid-cols-2 lg:grid-cols-4">
    {#each features as feature}
      <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
    {/each}
  </div>
</section>
