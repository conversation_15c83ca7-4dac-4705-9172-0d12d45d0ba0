<script>
  import { onMount } from 'svelte';
  import { fade, fly } from 'svelte/transition';
  import { Button } from '$lib/components/ui/button';
  import Logo from '$components/ui/Logo.svelte';

  let visible = false;

  onMount(() => {
    visible = true;
  });
</script>

<section class="bg-background text-foreground relative overflow-hidden border-b py-16 md:py-40">
  <!-- Animated background elements -->
  <div class="absolute inset-0 z-0 overflow-hidden">
    {#if visible}
      <div in:fade={{ duration: 1000, delay: 200 }} class="absolute right-20 top-20 opacity-5">
        <Logo className="h-64 w-64" />
      </div>
      <div
        in:fly={{ y: 100, duration: 1000, delay: 300 }}
        class="absolute bottom-20 left-40 opacity-5">
        <Logo className="h-48 w-48" />
      </div>
      <div
        in:fly={{ x: -100, duration: 1000, delay: 500 }}
        class="absolute right-1/4 top-1/2 opacity-5">
        <Logo className="h-56 w-56" />
      </div>
    {/if}
  </div>

  <div class="container relative z-10 mx-auto px-4">
    <div class="grid grid-cols-1 items-center gap-12 md:grid-cols-2">
      {#if visible}
        <div
          in:fly={{ y: 20, duration: 800 }}
          class="leading-tighter mb-8 w-[90%] text-4xl font-light md:text-5xl lg:text-[80px]">
          Streamline your <span class="gradient-text">Job Search</span>, get more interviews.
        </div>
        <p
          in:fly={{ y: 20, duration: 800, delay: 200 }}
          class="text-muted-foreground mb-12 md:text-2xl">
          Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings,
          auto-fills applications, and tracks your progress.
        </p>
        <div
          in:fly={{ y: 20, duration: 800, delay: 400 }}
          class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
          <Button
            class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-none border border-transparent p-8 text-lg font-medium transition-all duration-200">
            Get Started Free
          </Button>
          <Button
            class="border-border bg-background text-foreground hover:bg-accent hover:text-accent-foreground group flex items-center rounded-none border p-8 text-lg font-medium transition-all duration-200">
            How It Works
            <i
              class="fa fa-arrow-right ml-4 transition-transform duration-200 group-hover:translate-x-1"
            ></i>
          </Button>
        </div>
        <div
          in:fly={{ y: 20, duration: 800, delay: 600 }}
          class="text-muted-foreground mt-8 flex items-center text-sm">
          <div class="mr-3 flex -space-x-2">
            <img
              src="https://randomuser.me/api/portraits/women/79.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/men/32.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
            <img
              src="https://randomuser.me/api/portraits/women/44.jpg"
              alt="User"
              class="border-background h-8 w-8 rounded-full border-2" />
          </div>
          <span
            >Join <span class="font-semibold">10,000+</span> job seekers finding work faster</span>
        </div>
      {/if}
    </div>

    <!-- Image content -->
    <div class="relative hidden md:block">
      <div class="bg-muted/20 flex h-[400px] items-center justify-center rounded-lg p-8">
        <div class="text-center">
          <div
            class="bg-primary/20 mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
            <i class="text-primary fa fa-search text-2xl"></i>
          </div>
          <h3 class="mb-2 text-xl font-medium">Smart Job Matching</h3>
          <p class="text-muted-foreground">
            Our AI finds the perfect jobs for your skills and experience
          </p>
        </div>
      </div>
    </div>
  </div>
</section>
