﻿<script lang="ts">
  import JobCard from './JobCard.svelte';
  import { ScrollArea } from '$lib/components/ui/scroll-area';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { MoreHorizontal, Archive, Trash2, ArrowRight } from 'lucide-svelte';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { flip } from 'svelte/animate';

  let {
    filteredApplications = [],
    openApplicationDetails,
    selectedItems = new Set(),
    onSelectionChange = () => {},
    onBulkMove = (_targetStatus: string, _selectedIds: string[]) => {},
    columns = [],
  }: {
    filteredApplications: any[];
    openApplicationDetails: (application: any) => void;
    selectedItems: Set<string>;
    onSelectionChange: (itemId: string, selected: boolean) => void;
    onBulkMove: (targetStatus: string, selectedIds: string[]) => void;
    columns: any[];
  } = $props();

  const flipDurationMs = 300;

  // Check if all items are selected
  const allSelected = $derived(
    filteredApplications.length > 0 &&
      filteredApplications.every((item: any) => selectedItems.has(item.id.toString()))
  );

  // Check if some items are selected
  const someSelected = $derived(
    filteredApplications.some((item: any) => selectedItems.has(item.id.toString()))
  );

  // Handle select all/none
  function handleSelectAll() {
    if (allSelected) {
      // Deselect all
      filteredApplications.forEach((item: any) => {
        onSelectionChange(item.id.toString(), false);
      });
    } else {
      // Select all
      filteredApplications.forEach((item: any) => {
        onSelectionChange(item.id.toString(), true);
      });
    }
  }
</script>

<!-- Header with bulk actions -->
{#if selectedItems.size > 0}
  <div class="bg-muted/50 border-b p-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <Checkbox
          checked={allSelected}
          indeterminate={someSelected && !allSelected}
          onCheckedChange={handleSelectAll}
          aria-label="Select all applications" />
        <Badge variant="secondary">
          {selectedItems.size} selected
        </Badge>
      </div>

      <div class="flex items-center gap-2">
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="outline" size="sm">
              <MoreHorizontal class="mr-2 h-4 w-4" />
              Actions
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content>
            <DropdownMenu.Label>Move Selected To</DropdownMenu.Label>
            <DropdownMenu.Separator />
            {#each columns as targetColumn}
              <DropdownMenu.Item
                onclick={() => {
                  const selectedIds = Array.from(selectedItems);
                  onBulkMove(targetColumn.id, selectedIds);
                }}>
                <ArrowRight class="mr-2 h-4 w-4" />
                {targetColumn.name}
              </DropdownMenu.Item>
            {/each}
            <DropdownMenu.Separator />
            <DropdownMenu.Item>
              <Archive class="mr-2 h-4 w-4" />
              Archive Selected
            </DropdownMenu.Item>
            <DropdownMenu.Item class="text-destructive">
              <Trash2 class="mr-2 h-4 w-4" />
              Delete Selected
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </div>
    </div>
  </div>
{/if}

<ScrollArea class="h-[600px] w-full">
  <div class="space-y-3 p-4">
    {#if filteredApplications.length > 0}
      {#each filteredApplications as application (application.id)}
        <div animate:flip={{ duration: flipDurationMs }}>
          <JobCard
            {application}
            {openApplicationDetails}
            isSelected={selectedItems.has(application.id.toString())}
            onSelectionChange={(selected) =>
              onSelectionChange(application.id.toString(), selected)} />
        </div>
      {/each}
    {:else}
      <div class="flex h-32 items-center justify-center text-center">
        <div>
          <p class="text-muted-foreground">No applications found</p>
          <p class="text-muted-foreground text-sm">Try adjusting your filters</p>
        </div>
      </div>
    {/if}
  </div>
</ScrollArea>
