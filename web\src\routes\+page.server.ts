import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
  try {
    const jobCollections = await prisma.$queryRaw`
      SELECT * FROM cron.job_collections ORDER BY name ASC
    `;

    return {
      jobCollections,
    };
  } catch (error) {
    console.error('Error fetching job collections:', error);
    // Return empty array as fallback
    return {
      jobCollections: [],
    };
  }
};
