<script lang="ts">
  import * as Card from '$lib/components/ui/card/index.js';
  import * as Carousel from '$lib/components/ui/carousel/index.js';

  import Autoplay from 'embla-carousel-autoplay';
  import type { AlignmentOptionType } from 'embla-carousel/components/Alignment';

  const companies = [
    { name: 'Google', logo: '' },
    { name: 'Microsoft', logo: '' },
    { name: 'Amazon', logo: '' },
    { name: 'Apple', logo: '' },
    { name: 'Meta', logo: '' },
    { name: 'Netflix', logo: '' },
    { name: 'Uber', logo: '' },
    { name: 'Airbnb', logo: '' },
  ];

  // Create three arrays with different orderings for visual variety
  const companiesRow1 = [...companies];
  const companiesRow2 = [...companies].reverse();
  const companiesRow3 = [...companies].sort(() => Math.random() - 0.5);

  // Create autoplay plugins with different settings for each carousel
  const plugin1 = Autoplay({ delay: 2000, stopOnInteraction: true });
  const plugin2 = Autoplay({ delay: 4000, stopOnInteraction: true });
  const plugin3 = Autoplay({ delay: 2500, stopOnInteraction: true });

  // Set reverse direction for the middle carousel
  const options1 = { align: 'start' as AlignmentOptionType, loop: true };
  const options2 = { align: 'start' as AlignmentOptionType, loop: true };
  const options3 = { align: 'start' as const, loop: true };
</script>

<section class="bg-muted py-12">
  <div class="px-4">
    <!-- First row - left to right -->
    <div class="mb-8">
      <Carousel.Root
        plugins={[plugin1]}
        opts={options1}
        class="w-full"
        on:mouseenter={plugin1.stop}
        on:mouseleave={() => plugin1.play()}>
        <Carousel.Content>
          {#each companiesRow1 as company, i (i)}
            <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
              <div class="p-1">
                <Card.Root
                  class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                  <Card.Content class="flex h-24 items-center justify-center p-6">
                    <img
                      src={company.logo}
                      alt="{company.name} logo"
                      class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                      on:error={(e) =>
                        ((e.currentTarget as HTMLImageElement).src =
                          `https://placehold.co/200x80?text=${company.name}`)} />
                  </Card.Content>
                </Card.Root>
              </div>
            </Carousel.Item>
          {/each}
        </Carousel.Content>
      </Carousel.Root>
    </div>

    <!-- Second row - right to left -->
    <div class="mb-8">
      <Carousel.Root
        plugins={[plugin2]}
        opts={options2}
        class="w-full"
        on:mouseenter={plugin2.stop}
        on:mouseleave={() => plugin2.play()}>
        <Carousel.Content>
          {#each companiesRow2 as company, i (i)}
            <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
              <div class="p-1">
                <Card.Root
                  class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                  <Card.Content class="flex h-24 items-center justify-center p-6">
                    <img
                      src={company.logo}
                      alt="{company.name} logo"
                      class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                      on:error={(e) =>
                        ((e.currentTarget as HTMLImageElement).src =
                          `https://placehold.co/200x80?text=${company.name}`)} />
                  </Card.Content>
                </Card.Root>
              </div>
            </Carousel.Item>
          {/each}
        </Carousel.Content>
      </Carousel.Root>
    </div>

    <!-- Third row - left to right -->
    <div class="mb-8">
      <Carousel.Root
        plugins={[plugin3]}
        opts={options3}
        class="w-full"
        on:mouseenter={plugin3.stop}
        on:mouseleave={() => plugin3.play()}>
        <Carousel.Content>
          {#each companiesRow3 as company, i (i)}
            <Carousel.Item class="md:basis-1/4 lg:basis-1/6">
              <div class="p-1">
                <Card.Root
                  class="hover:border-primary/10 transition-all duration-300 hover:shadow-md">
                  <Card.Content class="flex h-24 items-center justify-center p-6">
                    <img
                      src={company.logo}
                      alt="{company.name} logo"
                      class="max-h-12 max-w-[120px] opacity-70 transition-all duration-300 hover:scale-105 hover:opacity-100 dark:opacity-60 dark:hover:opacity-90"
                      on:error={(e) =>
                        ((e.currentTarget as HTMLImageElement).src =
                          `https://placehold.co/200x80?text=${company.name}`)} />
                  </Card.Content>
                </Card.Root>
              </div>
            </Carousel.Item>
          {/each}
        </Carousel.Content>
      </Carousel.Root>
    </div>

    <p class="text-md text-muted-foreground mt-8 text-center">
      Join thousands of professionals who've found their dream jobs through our platform
    </p>
  </div>
</section>
