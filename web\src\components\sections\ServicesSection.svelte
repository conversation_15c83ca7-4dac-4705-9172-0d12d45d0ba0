<script lang="ts">
  import FeatureCard from '../ui/FeatureCard.svelte';
  import {
    Target,
    Clock,
    BarChart4,
    Shield,
    Globe,
    CheckCircle,
    FileText,
    PenTool,
    Award,
    ArrowRight,
    MessageSquare,
    Sparkles,
  } from 'lucide-svelte';

  // Features object with improved wording
  const features = {
    // Automated Apply features
    automatedApply: {
      title: 'Effortless Job Applications',
      description:
        'Submit applications to hundreds of positions with our streamlined one-click system.',
      secondary: [
        {
          icon: Clock,
          title: 'Reclaim Your Time',
          description:
            'Save hours daily by automating repetitive tasks and focus on interview preparation.',
        },
        {
          icon: BarChart4,
          title: 'Performance Insights',
          description:
            'Gain valuable analytics to optimize your application strategy and improve results.',
        },
        {
          icon: Shield,
          title: 'Resume Enhancement',
          description:
            'Receive tailored suggestions to strengthen your resume for specific opportunities.',
        },
        {
          icon: Globe,
          title: 'Universal Platform Support',
          description:
            'Seamlessly works with LinkedIn, Indeed, Glassdoor, ZipRecruiter, and many more.',
        },
      ],
    },

    // Job Tracker features
    jobTracker: {
      title: 'Comprehensive Application Tracking',
      description: 'Monitor all your job applications in one intuitive, centralized dashboard.',
      secondary: [
        {
          icon: CheckCircle,
          title: 'Real-time Status Updates',
          description: 'Track your progress through each stage of the hiring process with clarity.',
        },
        {
          icon: Clock,
          title: 'Interview Management',
          description:
            'Organize and prepare for upcoming interviews with smart scheduling and reminders.',
        },
        {
          icon: BarChart4,
          title: 'Strategic Analytics',
          description:
            'Visualize your job search journey with detailed metrics and actionable insights.',
        },
        {
          icon: Shield,
          title: 'Enterprise-grade Security',
          description:
            'Rest assured your career data is protected with advanced encryption and privacy controls.',
        },
      ],
    },

    // Resume Builder features
    resumeBuilder: {
      title: 'Professional Resume Creator',
      description: 'Craft standout resumes that capture attention with our intuitive builder.',
      main: [
        {
          icon: FileText,
          title: 'Expert-designed Templates',
          description:
            'Choose from dozens of ATS-optimized templates crafted by hiring professionals.',
        },
        {
          icon: PenTool,
          title: 'Intuitive Customization',
          description: 'Personalize every aspect of your resume with our user-friendly editor.',
        },
      ],
      secondary: [
        {
          icon: Target,
          title: 'ATS-Friendly Formatting',
          description:
            'Ensure your resume successfully navigates through automated screening systems.',
        },
        {
          icon: Award,
          title: 'Strategic Skills Showcase',
          description:
            'Automatically highlight relevant qualifications based on target job descriptions.',
        },
        {
          icon: Globe,
          title: 'Versatile Export Options',
          description:
            'Download your polished resume in PDF, DOCX, or plain text formats as needed.',
        },
        {
          icon: Shield,
          title: 'Multiple Resume Versions',
          description:
            'Create and manage specialized resumes tailored for different career opportunities.',
        },
      ],
    },

    // Co-Pilot features
    coPilot: {
      title: 'AI Career Co-Pilot',
      description: 'Navigate your career journey with AI-powered guidance every step of the way.',
      secondary: [
        {
          icon: MessageSquare,
          title: 'AI Interview Coach',
          description:
            'Practice with realistic mock interviews tailored to your industry with instant feedback.',
        },
        {
          icon: Sparkles,
          title: 'Personalized Insights',
          description: 'Receive custom career advice based on your skills, experience, and goals.',
        },
        {
          icon: Target,
          title: 'Job Match Analysis',
          description:
            'Get AI-powered compatibility scores for job listings based on your profile.',
        },
        {
          icon: Shield,
          title: 'Career Strategy Planning',
          description:
            'Develop a strategic roadmap to achieve your long-term professional objectives.',
        },
      ],
    },
  };
</script>

<section id="services" class="border-border border">
  <!-- Automated Apply Section -->
  <div class="flex flex-col">
    <!-- 2 columns row -->
    <div
      class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
      <div
        class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
        <div
          class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
          <div class="flex flex-col gap-20">
            <h3 class="font-light! max-w-3xs text-6xl">{features.automatedApply.title}</h3>
            <p class="typography font-montreal text-xl">
              {features.automatedApply.description}
            </p>
            <a
              href="/auto-apply"
              class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">
              Learn More <ArrowRight class="ml-2 h-4 w-4" />
            </a>
          </div>
        </div>
      </div>
      <div
        class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t">
      </div>
    </div>

    <!-- 4 columns row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      {#each features.automatedApply.secondary as feature}
        <div
          class="p-22 border-border bg-card text-card-foreground hover:bg-card/80 dark:hover:bg-card/90 hover:border-primary/20 group rounded-none border shadow-md transition-all duration-300 hover:shadow-lg">
          <div
            class="bg-primary/10 group-hover:bg-primary/20 mb-4 flex h-12 w-12 items-center justify-center rounded-lg transition-all duration-300">
            <svelte:component
              this={feature.icon}
              class="text-primary h-6 w-6 transition-all duration-300 group-hover:scale-110" />
          </div>
          <h3 class="font-normal! mb-4 text-3xl">{feature.title}</h3>
          <p class="text-md text-muted-foreground">{feature.description}</p>
        </div>
      {/each}
    </div>
  </div>

  <div
    class="border-border md:grid-cols-16 flex flex-col border [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
    <div
      class="bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 border-border col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l">
    </div>
    <div
      class="p-15 text-foreground col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
      <div
        class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
        <div class="flex flex-col gap-20">
          <h3 class="font-light! max-w-3xs text-6xl">{features.jobTracker.title}</h3>
          <p class="typography font-montreal text-xl">
            {features.jobTracker.description}
          </p>
          <a
            href="/job-tracker"
            class="bg-primary text-primary-foreground hover:bg-primary/90 dark:hover:bg-primary/70 group flex w-48 flex-row items-center justify-between rounded-md px-6 py-3 transition-all duration-200">
            Learn More <ArrowRight
              class="ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 4 columns row -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
    {#each features.jobTracker.secondary as feature}
      <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
    {/each}
  </div>

  <div
    class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
    <div
      class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
      <div
        class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
        <div class="flex flex-col gap-20">
          <h3 class="font-light! max-w-3xs text-6xl">{features.resumeBuilder.title}</h3>
          <p class="typography font-montreal text-xl">
            {features.resumeBuilder.description}
          </p>
          <a
            href="/resume-builder"
            class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">
            Learn More <ArrowRight class="ml-2 h-4 w-4" />
          </a>
        </div>
      </div>
    </div>
    <div
      class="border-border bg-grid col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t">
    </div>
  </div>

  <!-- 4 columns row -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
    {#each features.resumeBuilder.secondary as feature}
      <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
    {/each}
  </div>

  <!-- Co-Pilot Section -->
  <div
    class="border-border md:grid-cols-16 flex flex-col border border-t [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]">
    <div
      class="border-border bg-grid bg-grid-primary/20 dark:bg-grid-primary/40 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l">
    </div>
    <div
      class="p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center">
      <div
        class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]">
        <div class="flex flex-col gap-20">
          <h3 class="font-light! max-w-3xs text-6xl">{features.coPilot.title}</h3>
          <p class="typography font-montreal text-xl">
            {features.coPilot.description}
          </p>
          <a
            href="/co-pilot"
            class="bg-primary text-primary-foreground hover:bg-primary/90 flex w-48 items-center rounded-none border border-transparent p-6 text-lg font-medium transition-colors">
            Learn More <ArrowRight class="ml-2 h-4 w-4" />
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 4 columns row for Co-Pilot features -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
    {#each features.coPilot.secondary as feature}
      <FeatureCard icon={feature.icon} title={feature.title} description={feature.description} />
    {/each}
  </div>
</section>
