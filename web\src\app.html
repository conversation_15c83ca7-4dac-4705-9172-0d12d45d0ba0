<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Auto Apply</title>

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="48x48" href="/assets/favicon/favicon-48x48.png" />
    <link rel="icon" type="image/png" sizes="64x64" href="/assets/favicon/favicon-64x64.png" />
    <link rel="icon" type="image/png" sizes="128x128" href="/assets/favicon/favicon-128x128.png" />
    <link rel="icon" type="image/png" sizes="256x256" href="/assets/favicon/favicon-256x256.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="/assets/favicon/favicon-192x192.png" />
    <link rel="icon" type="image/png" sizes="512x512" href="/assets/favicon/favicon-512x512.png" />

    <!-- Fallback ICO -->
    <link rel="shortcut icon" href="/assets/favicon/favicon.ico" type="image/x-icon" />

    <!-- Web App Manifest -->
    <link rel="manifest" href="/assets/favicon/manifest.json" />

    <!-- Theme color for browser UI -->
    <meta name="theme-color" content="#000000" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />

    <meta
      http-equiv="Content-Security-Policy"
      content="default-src 'self'; script-src 'self' https://js.stripe.com https://checkout.stripe.com https://m.stripe.network 'unsafe-inline'; connect-src 'self' https://api.stripe.com https://checkout.stripe.com ws: wss: http://localhost:*; frame-src 'self' https://js.stripe.com https://hooks.stripe.com https://checkout.stripe.com; img-src 'self' data: https://*.stripe.com; style-src 'self' 'unsafe-inline';"
    />

    %sveltekit.head%
  </head>
  <body data-sveltekit-preload-data="hover">
    <main class="h-full">%sveltekit.body%</main>
  </body>
</html>
