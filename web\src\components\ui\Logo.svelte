<script lang="ts">
  export let className: string = '';
</script>

<svg
  class={className}
  fill="none"
  viewBox="0 0 256 256"
  xmlns="http://www.w3.org/2000/svg"
  height="32"
  width="32"
  stroke="currentColor"
  {...$$restProps}>
  <rect fill="none" height="240" stroke="currentColor" stroke-width="30" width="240" x="8" y="8" />
  <path
    d="M80 130 L110 160 L180 90"
    fill="none"
    stroke="currentColor"
    stroke-dasharray="300"
    stroke-dashoffset="300"
    stroke-linecap="round"
    stroke-linejoin="round"
    stroke-width="26">
    <animate
      attributeName="stroke-dashoffset"
      begin="0.2s"
      dur="0.6s"
      fill="freeze"
      from="300"
      to="0" />
  </path>
</svg>
