<!-- src/components/help/HelpArticleCard.svelte -->
<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';
  import { formatDistanceToNow } from 'date-fns';
  import * as lucideIcons from 'lucide-svelte';

  // Props
  let { article, className = '' } = $props<{
    article: {
      id: string;
      title: string;
      slug: string;
      excerpt?: string;
      updatedAt: string;
      viewCount: number;
      category: {
        id: string;
        name: string;
        slug: string;
        icon?: string;
      };
      tags: Array<{
        id: string;
        name: string;
        slug: string;
      }>;
    };
    className?: string;
  }>();

  // We don't need the iconComponent variable anymore since we're using direct component references

  // Format the updated date
  let updatedDate = $derived(formatDistanceToNow(new Date(article.updatedAt), { addSuffix: true }));
</script>

<Card.Root class="h-full overflow-hidden {className}">
  <Card.Header>
    <div class="flex items-center gap-2">
      <Badge variant="outline" class="flex items-center gap-1">
        {#if article.category.icon === 'BookOpen'}
          <lucideIcons.BookOpen class="h-3 w-3" />
        {:else if article.category.icon === 'FileText'}
          <lucideIcons.FileText class="h-3 w-3" />
        {:else if article.category.icon === 'CreditCard'}
          <lucideIcons.CreditCard class="h-3 w-3" />
        {:else if article.category.icon === 'Shield'}
          <lucideIcons.Shield class="h-3 w-3" />
        {:else}
          <lucideIcons.FileText class="h-3 w-3" />
        {/if}
        <span>{article.category.name}</span>
      </Badge>
      <span class="text-muted-foreground text-xs">Updated {updatedDate}</span>
    </div>
    <Card.Title class="mt-2">
      <a href="/help/{article.slug}" class="hover:text-primary hover:underline">
        {article.title}
      </a>
    </Card.Title>
    {#if article.excerpt}
      <Card.Description class="line-clamp-2">{article.excerpt}</Card.Description>
    {/if}
  </Card.Header>
  <Card.Footer>
    <div class="flex flex-wrap gap-2">
      {#each (article.tags || []).slice(0, 3) as tag}
        <Badge variant="secondary" class="text-xs">
          {tag.name}
        </Badge>
      {/each}
      {#if (article.tags || []).length > 3}
        <Badge variant="secondary" class="text-xs">+{(article.tags || []).length - 3} more</Badge>
      {/if}
    </div>
  </Card.Footer>
</Card.Root>
